# 出货线（Delivery Line）系统实现文档

## 系统概述

出货线系统是Moofun奶牛农场游戏的核心变现设施，负责将累积的牛奶自动打包成「牛奶方塊」（Milk Block），并将其出售获得宝石（Gem）收入。

## 数据模型

### DeliveryLine 模型

```typescript
interface DeliveryLineAttributes {
  id: number;
  walletId: number;
  level: number; // 出货线等级
  deliverySpeed: number; // 出货速度（秒/次）
  blockUnit: number; // 方块单位（牛奶/方块）
  blockPrice: number; // 方块价格（GEM/方块）
  upgradeCost: number; // 升级费用
  lastDeliveryTime: Date; // 上次出货时间
  pendingMilk: number; // 待处理的牛奶量
  pendingBlocks: number; // 待出售的牛奶方块数量
}
```

## 初始设定

- 出货速度：1秒/次
- 方块单位：5牛奶/方块
- 方块价格：5 GEM/方块
- 初始升级费用：500 GEM

## 升级机制

每次升级时：
- 出货线等级+1
- 方块单位提升2.0倍
- 方块价格提升2.0倍
- 出货速度提升1%（秒数 / 1.01）
- 下次升级费用提升2.0倍

## 自动出货机制

1. 系统自动将累积的牛奶打包成牛奶方块
2. 当牛奶数量达到方块单位要求时，自动创建牛奶方块
3. 按照设定的出货速度，自动出售牛奶方块获得GEM

## 核心功能

### 1. 初始化出货线

```typescript
public async initializeUserDeliveryLine(walletId: number): Promise<DeliveryLine>
```

为新用户创建出货线，设置初始参数。

### 2. 升级出货线

```typescript
public async upgradeDeliveryLine(walletId: number): Promise<DeliveryLine>
```

升级出货线，提升各项参数，消耗用户GEM。

### 3. 添加牛奶到出货线

```typescript
public async addMilkToDeliveryLine(walletId: number, milkAmount: number): Promise<DeliveryLine>
```

将牛奶添加到出货线，自动创建牛奶方块。

### 4. 处理出货

```typescript
public async processDelivery(deliveryLine: DeliveryLine, transaction?: any): Promise<number>
```

处理出货，将牛奶方块转换为GEM收入。

### 5. 计算离线收益

```typescript
public async calculateOfflineEarnings(walletId: number, offlineTime: number): Promise<number>
```

计算用户离线期间的出货收益。

## API接口

### 1. 获取用户出货线

- **URL**: `/api/delivery/delivery-line`
- **Method**: GET
- **描述**: 获取用户的出货线信息
- **返回示例**:
  ```json
  {
    "success": true,
    "data": {
      "deliveryLine": {
        "id": 1,
        "walletId": 123,
        "level": 1,
        "deliverySpeed": 5,
        "blockUnit": 5,
        "blockPrice": 5,
        "upgradeCost": 500,
        "lastDeliveryTime": "2023-06-01T12:00:00Z",
        "pendingMilk": 3,
        "pendingBlocks": 2
      }
    }
  }
  ```

### 2. 升级出货线

- **URL**: `/api/delivery/delivery-line/upgrade`
- **Method**: POST
- **描述**: 升级用户的出货线
- **返回示例**:
  ```json
  {
    "success": true,
    "data": {
      "deliveryLine": {
        "id": 1,
        "walletId": 123,
        "level": 2,
        "deliverySpeed": 4.95,
        "blockUnit": 10,
        "blockPrice": 10,
        "upgradeCost": 1000,
        "lastDeliveryTime": "2023-06-01T12:00:00Z",
        "pendingMilk": 3,
        "pendingBlocks": 2
      }
    }
  }
  ```

### 3. 添加牛奶到出货线

- **URL**: `/api/delivery/delivery-line/add-milk`
- **Method**: POST
- **Body**: `{ "milkAmount": 50 }`
- **描述**: 将指定数量的牛奶添加到出货线
- **返回示例**:
  ```json
  {
    "success": true,
    "data": {
      "deliveryLine": {
        "id": 1,
        "walletId": 123,
        "level": 2,
        "deliverySpeed": 4.95,
        "blockUnit": 10,
        "blockPrice": 10,
        "upgradeCost": 1000,
        "lastDeliveryTime": "2023-06-01T12:00:00Z",
        "pendingMilk": 3,
        "pendingBlocks": 12
      }
    }
  }
  ```

### 4. 计算离线收益

- **URL**: `/api/delivery/delivery-line/offline-earnings`
- **Method**: POST
- **Body**: `{ "offlineTime": 3600 }`
- **描述**: 计算用户离线期间的出货收益
- **返回示例**:
  ```json
  {
    "success": true,
    "data": {
      "earnedGem": 120
    }
  }
  ```

## 使用示例

### 初始化并获取用户出货线

```typescript
const deliveryLine = await deliveryLineService.getUserDeliveryLine(walletId);
```

### 升级出货线

```typescript
const upgradedLine = await deliveryLineService.upgradeDeliveryLine(walletId);
```

### 添加牛奶到出货线

```typescript
const updatedLine = await deliveryLineService.addMilkToDeliveryLine(walletId, milkAmount);
```

### 计算离线收益

```typescript
const earnedGem = await deliveryLineService.calculateOfflineEarnings(walletId, offlineTime);
```

## 注意事项

1. 出货线系统与用户钱包（UserWallet）紧密关联，需确保用户钱包存在
2. 所有涉及资源变动的操作都使用事务确保数据一致性
3. 离线收益计算考虑了时间差和资源上限
4. 系统自动处理牛奶到方块的转换和方块到GEM的出售过程