import express from 'express';
import multer from 'multer';
import path from 'path';
import deliveryLineController from '../controllers/deliveryLineController';
import { walletAuthMiddleware } from '../middlewares/walletAuth';

const router = express.Router();

// 配置multer用于文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    cb(null, `delivery-line-config-${Date.now()}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  fileFilter: (req, file, cb) => {
    if (file.mimetype.includes('excel') || file.mimetype.includes('spreadsheet') || file.originalname.endsWith('.xlsx')) {
      cb(null, true);
    } else {
      cb(new Error('只支持Excel文件格式'));
    }
  },
  limits: { fileSize: 5 * 1024 * 1024 } // 5MB限制
});

// 所有路由都需要钱包认证
router.use(walletAuthMiddleware);

// 获取用户的出货线
router.get('/delivery-line', deliveryLineController.getUserDeliveryLine);

// 升级出货线
router.post('/delivery-line/upgrade', deliveryLineController.upgradeDeliveryLine);

// 添加牛奶到出货线
router.post('/delivery-line/add-milk', deliveryLineController.addMilkToDeliveryLine);

// 计算离线收益
router.post('/delivery-line/offline-earnings', deliveryLineController.calculateOfflineEarnings);

// 出售牛奶方块换取宝石
router.post('/delivery-line/sell-blocks', deliveryLineController.sellMilkBlocks);

// 配置管理路由
router.get('/delivery-line/configs', deliveryLineController.getAllConfigs);
router.post('/delivery-line/upload-config',
  upload.single('configFile'),
  deliveryLineController.uploadDeliveryLineConfig
);

export default router;