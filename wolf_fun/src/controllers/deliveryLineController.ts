import { Request, Response } from 'express';
import deliveryLineService from '../services/deliveryLineService';
import { MyRequest } from '../types/customRequest';
import { responseUtil } from '../utils/responseUtil';

class DeliveryLineController {
  // 获取用户的出货线
  public async getUserDeliveryLine(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = req.user?.walletId;
      if (!walletId) {
        responseUtil.error(res, '未授权', 401);
        return;
      }

      // 初始化并获取用户的出货线
      const deliveryLine = await deliveryLineService.getUserDeliveryLine(walletId);
      responseUtil.success(res, { deliveryLine });
    } catch (error: any) {
      responseUtil.error(res, error.message, 500);
    }
  }

  // 升级出货线
  public async upgradeDeliveryLine(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = req.user?.walletId;
      if (!walletId) {
        responseUtil.error(res, '未授权', 401);
        return;
      }

      // 升级出货线
      const deliveryLine = await deliveryLineService.upgradeDeliveryLine(walletId);
      responseUtil.success(res, { deliveryLine });
    } catch (error: any) {
      responseUtil.error(res, error.message, 500);
    }
  }

  // 添加牛奶到出货线
  public async addMilkToDeliveryLine(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = req.user?.walletId;
      const { milkAmount } = req.body;

      if (!walletId) {
        responseUtil.error(res, '未授权', 401);
        return;
      }

      if (!milkAmount || isNaN(Number(milkAmount)) || Number(milkAmount) <= 0) {
        responseUtil.error(res, '无效的牛奶数量', 400);
        return;
      }

      // 添加牛奶到出货线
      const deliveryLine = await deliveryLineService.addMilkToDeliveryLine(walletId, Number(milkAmount));
      responseUtil.success(res, { deliveryLine });
    } catch (error: any) {
      responseUtil.error(res, error.message, 500);
    }
  }

  // 计算离线收益
  public async calculateOfflineEarnings(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = req.user?.walletId;
      const { offlineTime } = req.body;

      if (!walletId) {
        responseUtil.error(res, '未授权', 401);
        return;
      }

      if (!offlineTime || isNaN(Number(offlineTime)) || Number(offlineTime) < 0) {
        responseUtil.error(res, '无效的离线时间', 400);
        return;
      }

      // 计算离线收益
      const earnedGem = await deliveryLineService.calculateOfflineEarnings(walletId, Number(offlineTime));
      responseUtil.success(res, { earnedGem });
    } catch (error: any) {
      responseUtil.error(res, error.message, 500);
    }
  }
  
  // 出售牛奶方块换取宝石
  public async sellMilkBlocks(req: MyRequest, res: Response): Promise<void> {
    try {
      const walletId = req.user?.walletId;
      const { blockCount } = req.body;

      if (!walletId) {
        responseUtil.error(res, '未授权', 401);
        return;
      }

      if (!blockCount || isNaN(Number(blockCount)) || Number(blockCount) <= 0) {
        responseUtil.error(res, '无效的方块数量', 400);
        return;
      }

      // 出售牛奶方块
      const { soldBlocks, earnedGems } = await deliveryLineService.sellMilkBlocks(walletId, Number(blockCount));
      responseUtil.success(res, { 
        soldBlocks,
        earnedGems,
        message: `成功出售 ${soldBlocks} 个牛奶方块，获得 ${earnedGems} 宝石`
      });
    } catch (error: any) {
      responseUtil.error(res, error.message, 500);
    }
  }
}

export default new DeliveryLineController();