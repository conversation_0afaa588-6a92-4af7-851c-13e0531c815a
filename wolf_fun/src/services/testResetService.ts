import { FarmPlot } from '../models/FarmPlot';
import { DeliveryLine } from '../models/DeliveryLine';
import { UserWallet } from '../models/UserWallet';
import { Transaction } from 'sequelize';
import { sequelize } from "../config/db";
import { FarmPlotCalculator, DeliveryLineCalculator, formatToThreeDecimalsNumber } from '../utils/bigNumberConfig';
import { logger } from '../utils/logger';

/**
 * 测试重置服务
 * 用于在测试环境下重置用户的游戏状态
 * 仅在开发环境下可用
 */
class TestResetService {
  
  /**
   * 重置用户的所有农场区块到初始状态
   * @param walletId 用户钱包ID
   * @param transaction 数据库事务
   * @returns 重置后的农场区块数据
   */
  public async resetUserFarmPlots(walletId: number, transaction?: Transaction): Promise<FarmPlot[]> {
    logger.info('开始重置用户农场区块', { walletId });
    
    try {
      // 获取用户的所有农场区块
      const farmPlots = await FarmPlot.findAll({
        where: { walletId },
        order: [['plotNumber', 'ASC']],
        transaction
      });

      if (farmPlots.length === 0) {
        logger.warn('用户没有农场区块数据', { walletId });
        return [];
      }

      // 获取当前激活的配置
      const { FarmConfigService } = require('./farmConfigService');
      const configs = await FarmConfigService.getCurrentConfig();

      if (configs.length === 0) {
        logger.warn('没有找到激活的农场配置，使用降级方案重置', { walletId });
        return await this.resetUserFarmPlotsWithFallback(walletId, farmPlots, transaction);
      }

      logger.info('使用新配置系统重置农场区块', {
        walletId,
        configCount: configs.length
      });

      // 获取等级1的配置
      const level1Config = configs.find((c: any) => c.grade === 1);

      if (!level1Config) {
        throw new Error('找不到等级1的配置数据');
      }

      // 重置每个农场区块到初始状态
      const resetPromises = farmPlots.map(async (plot) => {
        const isFirstPlot = plot.plotNumber === 1;
        
        // 重置到初始状态
        plot.level = 1;
        plot.barnCount = isFirstPlot ? level1Config.cow : 0;
        plot.milkProduction = isFirstPlot ? level1Config.production : 0; // 使用 production 字段而不是 milk
        plot.productionSpeed = isFirstPlot ? level1Config.speed : 100; // 默认速度
        plot.upgradeCost = isFirstPlot ? level1Config.cost : 0; // 使用等级1配置的 cost
        plot.lastProductionTime = new Date();
        plot.isUnlocked = isFirstPlot; // 只有第一个农场区块默认解锁
        plot.accumulatedMilk = 0; // 清空累积牛奶
        
        // 重新计算解锁费用
        if (plot.plotNumber > 1) {
          plot.unlockCost = FarmPlotCalculator.calculateUnlockCost(plot.plotNumber);
        } else {
          plot.unlockCost = 0; // 第一个农场区块无需解锁费用
        }
        
        await plot.save({ transaction });
        
        logger.debug('农场区块重置完成', {
          walletId,
          plotNumber: plot.plotNumber,
          level: plot.level,
          isUnlocked: plot.isUnlocked,
          milkProduction: plot.milkProduction,
          productionSpeed: plot.productionSpeed,
          upgradeCost: plot.upgradeCost,
          unlockCost: plot.unlockCost
        });
        
        return plot;
      });

      const resetFarmPlots = await Promise.all(resetPromises);
      
      logger.info('用户农场区块重置完成', {
        walletId,
        totalPlots: resetFarmPlots.length,
        unlockedPlots: resetFarmPlots.filter(p => p.isUnlocked).length
      });
      
      return resetFarmPlots;
    } catch (error) {
      logger.error('重置用户农场区块失败', {
        walletId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 使用降级方案重置用户农场区块（当配置系统不可用时）
   * @param walletId 用户钱包ID
   * @param farmPlots 农场区块数组
   * @param transaction 数据库事务
   * @returns 重置后的农场区块数据
   */
  private async resetUserFarmPlotsWithFallback(walletId: number, farmPlots: FarmPlot[], transaction?: Transaction): Promise<FarmPlot[]> {
    logger.info('使用降级方案重置用户农场区块', { walletId });

    const resetPromises = farmPlots.map(async (plot) => {
      const isFirstPlot = plot.plotNumber === 1;

      // 重置到初始状态（使用降级方案）
      plot.level = 1;
      plot.barnCount = isFirstPlot ? FarmPlotCalculator.calculateBarnCountSync(1) : 0;
      plot.milkProduction = isFirstPlot ? FarmPlotCalculator.calculateBaseProductionSync(1, plot.plotNumber) : 0;
      plot.productionSpeed = isFirstPlot ? FarmPlotCalculator.calculateProductionSpeedSync(1) : 100;
      plot.upgradeCost = isFirstPlot ? Math.round(100 * Math.pow(1.5, 1) * plot.plotNumber) : 0;
      plot.lastProductionTime = new Date();
      plot.isUnlocked = isFirstPlot;
      plot.accumulatedMilk = 0;

      // 重新计算解锁费用
      if (plot.plotNumber > 1) {
        plot.unlockCost = FarmPlotCalculator.calculateUnlockCost(plot.plotNumber);
      } else {
        plot.unlockCost = 0;
      }

      await plot.save({ transaction });

      logger.debug('农场区块重置完成（降级方案）', {
        walletId,
        plotNumber: plot.plotNumber,
        level: plot.level,
        isUnlocked: plot.isUnlocked,
        milkProduction: plot.milkProduction,
        productionSpeed: plot.productionSpeed,
        upgradeCost: plot.upgradeCost,
        unlockCost: plot.unlockCost
      });

      return plot;
    });

    const resetFarmPlots = await Promise.all(resetPromises);

    logger.info('用户农场区块重置完成（降级方案）', {
      walletId,
      totalPlots: resetFarmPlots.length,
      unlockedPlots: resetFarmPlots.filter(p => p.isUnlocked).length
    });

    return resetFarmPlots;
  }

  /**
   * 重置用户的配送线到初始状态
   * @param walletId 用户钱包ID
   * @param transaction 数据库事务
   * @returns 重置后的配送线数据
   */
  public async resetUserDeliveryLine(walletId: number, transaction?: Transaction): Promise<DeliveryLine | null> {
    logger.info('开始重置用户配送线', { walletId });
    
    try {
      // 获取用户的配送线
      let deliveryLine = await DeliveryLine.findOne({
        where: { walletId },
        transaction
      });

      if (!deliveryLine) {
        // 如果用户没有配送线，创建一个新的
        logger.info('用户没有配送线，创建新的配送线', { walletId });
        deliveryLine = await DeliveryLine.create({
          walletId,
          level: 1,
          deliverySpeed: 1, // 1秒/次
          blockUnit: 5, // 5牛奶/方块
          blockPrice: 5, // 5 GEM/方块
          upgradeCost: 500, // 初始升级费用
          lastDeliveryTime: new Date(),
          pendingMilk: 0,
          pendingBlocks: 0
        }, { transaction });
      } else {
        // 重置现有配送线到初始状态
        deliveryLine.level = 1;
        deliveryLine.deliverySpeed = 1; // 1秒/次
        deliveryLine.blockUnit = 5; // 5牛奶/方块
        deliveryLine.blockPrice = 5; // 5 GEM/方块
        deliveryLine.upgradeCost = 500; // 初始升级费用
        deliveryLine.lastDeliveryTime = new Date();
        deliveryLine.pendingMilk = 0; // 清空待处理牛奶
        deliveryLine.pendingBlocks = 0; // 清空待出售方块
        
        await deliveryLine.save({ transaction });
      }
      
      logger.info('用户配送线重置完成', {
        walletId,
        level: deliveryLine.level,
        deliverySpeed: deliveryLine.deliverySpeed,
        blockUnit: deliveryLine.blockUnit,
        blockPrice: deliveryLine.blockPrice,
        upgradeCost: deliveryLine.upgradeCost,
        pendingMilk: deliveryLine.pendingMilk,
        pendingBlocks: deliveryLine.pendingBlocks
      });
      
      return deliveryLine;
    } catch (error) {
      logger.error('重置用户配送线失败', {
        walletId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 重置用户的完整游戏状态
   * @param walletId 用户钱包ID
   * @returns 重置后的游戏状态数据
   */
  public async resetUserGameState(walletId: number): Promise<{
    farmPlots: FarmPlot[];
    deliveryLine: DeliveryLine | null;
    resetTimestamp: Date;
  }> {
    logger.info('开始重置用户完整游戏状态', { walletId });
    
    // 验证用户是否存在
    const userWallet = await UserWallet.findByPk(walletId);
    if (!userWallet) {
      const error = new Error(`用户钱包不存在: ${walletId}`);
      logger.error('重置游戏状态失败', {
        walletId,
        error: error.message
      });
      throw error;
    }

    const transaction = await sequelize.transaction();

    try {
      // 重置农场区块
      const farmPlots = await this.resetUserFarmPlots(walletId, transaction);

      // 重置配送线
      const deliveryLine = await this.resetUserDeliveryLine(walletId, transaction);

      // 更新用户最后活跃时间，确保用户被认为在线
      await UserWallet.update(
        { lastActiveTime: new Date() },
        {
          where: { id: walletId },
          transaction
        }
      );

      // 提交事务
      await transaction.commit();
      
      const resetTimestamp = new Date();
      
      logger.info('用户完整游戏状态重置成功', {
        walletId,
        farmPlotsCount: farmPlots.length,
        deliveryLineReset: !!deliveryLine,
        resetTimestamp
      });
      
      return {
        farmPlots,
        deliveryLine,
        resetTimestamp
      };
    } catch (error) {
      await transaction.rollback();
      logger.error('重置用户完整游戏状态失败', {
        walletId,
        error: error instanceof Error ? error.message : '未知错误'
      });
      throw error;
    }
  }

  /**
   * 获取当前环境信息
   * @returns 当前环境名称
   */
  public getCurrentEnvironment(): string {
    return process.env.NODE_ENV || 'unknown';
  }

  /**
   * 获取重置操作的安全检查信息
   * @param walletId 用户钱包ID
   * @returns 安全检查信息
   */
  public async getResetSafetyInfo(walletId: number): Promise<{
    environment: string;
    isDevelopment: boolean;
    userExists: boolean;
    farmPlotsCount: number;
    hasDeliveryLine: boolean;
    userWalletInfo?: {
      id: number;
      walletAddress: string | undefined;
      createdAt: Date;
      lastActiveTime?: Date;
    };
    farmPlotsDetails?: {
      unlockedCount: number;
      totalLevels: number;
      totalAccumulatedMilk: number;
    };
    deliveryLineDetails?: {
      level: number;
      pendingMilk: number;
      pendingBlocks: number;
    };
  }> {
    const userWallet = await UserWallet.findByPk(walletId);
    const farmPlots = await FarmPlot.findAll({ where: { walletId } });
    const deliveryLine = await DeliveryLine.findOne({ where: { walletId } });

    // 计算农场区块详细信息
    const farmPlotsDetails = farmPlots.length > 0 ? {
      unlockedCount: farmPlots.filter(p => p.isUnlocked).length,
      totalLevels: farmPlots.reduce((sum, p) => sum + p.level, 0),
      totalAccumulatedMilk: farmPlots.reduce((sum, p) => sum + p.accumulatedMilk, 0)
    } : undefined;

    return {
      environment: this.getCurrentEnvironment(),
      isDevelopment: this.getCurrentEnvironment() === 'development',
      userExists: !!userWallet,
      farmPlotsCount: farmPlots.length,
      hasDeliveryLine: !!deliveryLine,
      userWalletInfo: userWallet ? {
        id: userWallet.id,
        walletAddress: userWallet.walletAddress,
        createdAt: userWallet.createdAt,
        lastActiveTime: userWallet.lastActiveTime
      } : undefined,
      farmPlotsDetails,
      deliveryLineDetails: deliveryLine ? {
        level: deliveryLine.level,
        pendingMilk: deliveryLine.pendingMilk,
        pendingBlocks: deliveryLine.pendingBlocks
      } : undefined
    };
  }

  /**
   * 记录重置操作的审计日志
   * @param walletId 用户钱包ID
   * @param operation 操作类型
   * @param beforeState 重置前状态
   * @param afterState 重置后状态
   * @param requestInfo 请求信息
   */
  public logResetAudit(
    walletId: number,
    operation: string,
    beforeState: any,
    afterState: any,
    requestInfo: {
      requestId: string;
      ip?: string;
      userAgent?: string;
      timestamp: Date;
    }
  ): void {
    logger.info('游戏重置审计日志', {
      auditType: 'GAME_RESET',
      walletId,
      operation,
      beforeState,
      afterState,
      requestInfo,
      environment: process.env.NODE_ENV,
      serverTimestamp: new Date().toISOString()
    });
  }

  /**
   * 验证重置操作的前置条件
   * @param walletId 用户钱包ID
   * @returns 验证结果
   */
  public async validateResetPreconditions(walletId: number): Promise<{
    isValid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // 记录当前环境信息（不再限制环境）
      logger.info('重置操作环境信息', {
        environment: this.getCurrentEnvironment(),
        walletId
      });

      // 检查用户是否存在
      const userWallet = await UserWallet.findByPk(walletId);
      if (!userWallet) {
        errors.push(`用户钱包不存在: ${walletId}`);
        return { isValid: false, errors, warnings };
      }

      // 检查用户数据状态
      const farmPlots = await FarmPlot.findAll({ where: { walletId } });
      const deliveryLine = await DeliveryLine.findOne({ where: { walletId } });

      if (farmPlots.length === 0) {
        warnings.push('用户没有农场区块数据，将创建默认数据');
      } else {
        const unlockedPlots = farmPlots.filter(p => p.isUnlocked);
        const totalAccumulatedMilk = farmPlots.reduce((sum, p) => sum + p.accumulatedMilk, 0);

        if (unlockedPlots.length > 1) {
          warnings.push(`用户有 ${unlockedPlots.length} 个已解锁的农场区块，重置后只有第一个会保持解锁状态`);
        }

        if (totalAccumulatedMilk > 0) {
          warnings.push(`用户有 ${totalAccumulatedMilk.toFixed(3)} 单位累积牛奶，重置后将清零`);
        }
      }

      if (!deliveryLine) {
        warnings.push('用户没有配送线数据，将创建默认配送线');
      } else {
        if (deliveryLine.level > 1) {
          warnings.push(`配送线等级为 ${deliveryLine.level}，重置后将变为等级 1`);
        }

        if (deliveryLine.pendingMilk > 0 || deliveryLine.pendingBlocks > 0) {
          warnings.push(`配送线有待处理数据（牛奶: ${deliveryLine.pendingMilk}, 方块: ${deliveryLine.pendingBlocks}），重置后将清零`);
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings
      };
    } catch (error) {
      errors.push(`验证重置前置条件时发生错误: ${error instanceof Error ? error.message : '未知错误'}`);
      return { isValid: false, errors, warnings };
    }
  }
}

export default new TestResetService();
